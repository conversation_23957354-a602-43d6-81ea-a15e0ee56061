import React from 'react';
import { Box, Typography, Container, Paper, Button } from '@mui/material';
import ApiIcon from '@mui/icons-material/Api';
import CloudSyncIcon from '@mui/icons-material/CloudSync';
import SecurityIcon from '@mui/icons-material/Security';
import CodeIcon from '@mui/icons-material/Code';
import SettingsIcon from '@mui/icons-material/Settings';
import Header from '../../components/Header/Header';

interface IntegrationCardProps {
  title: string;
  description: string;
  icon: React.ReactNode;
  status: 'connected' | 'available' | 'coming-soon';
}

const IntegrationCard: React.FC<IntegrationCardProps> = ({ title, description, icon, status }) => {
  const getStatusColor = () => {
    switch (status) {
      case 'connected':
        return '#4CAF50';
      case 'available':
        return '#2196F3';
      case 'coming-soon':
        return '#9E9E9E';
      default:
        return '#9E9E9E';
    }
  };

  const getStatusText = () => {
    switch (status) {
      case 'connected':
        return 'Conectado';
      case 'available':
        return 'Disponível';
      case 'coming-soon':
        return 'Em Breve';
      default:
        return '';
    }
  };

  return (
    <Paper
      sx={{
        p: 3,
        height: '100%',
        display: 'flex',
        flexDirection: 'column',
        transition: 'transform 0.2s, box-shadow 0.2s',
        '&:hover': {
          transform: 'translateY(-4px)',
          boxShadow: 3,
        },
      }}
    >
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'flex-start', mb: 2 }}>
        <Box sx={{ color: getStatusColor() }}>
          {icon}
        </Box>
        <Typography
          variant="caption"
          sx={{
            color: getStatusColor(),
            bgcolor: `${getStatusColor()}15`,
            px: 1,
            py: 0.5,
            borderRadius: 1,
          }}
        >
          {getStatusText()}
        </Typography>
      </Box>
      <Typography variant="h6" gutterBottom>
        {title}
      </Typography>
      <Typography variant="body2" color="text.secondary" sx={{ mb: 3 }}>
        {description}
      </Typography>
      <Button
        variant="outlined"
        color="primary"
        sx={{ mt: 'auto' }}
        disabled={status === 'coming-soon'}
      >
        {status === 'connected' ? 'Configurar' : status === 'available' ? 'Conectar' : 'Em Breve'}
      </Button>
    </Paper>
  );
};

const integrations = [
  {
    title: 'API REST',
    description: 'Integre sua aplicação através de nossa API REST completa e documentada.',
    icon: <ApiIcon sx={{ fontSize: 40 }} />,
    status: 'connected' as const,
  },
  {
    title: 'Cloud Sync',
    description: 'Sincronize seus dados com serviços de nuvem populares.',
    icon: <CloudSyncIcon sx={{ fontSize: 40 }} />,
    status: 'available' as const,
  },
  {
    title: 'Autenticação Segura',
    description: 'Integração com provedores de autenticação OAuth 2.0.',
    icon: <SecurityIcon sx={{ fontSize: 40 }} />,
    status: 'connected' as const,
  },
  {
    title: 'Webhooks',
    description: 'Configure webhooks para receber atualizações em tempo real.',
    icon: <CodeIcon sx={{ fontSize: 40 }} />,
    status: 'available' as const,
  },
  {
    title: 'ERP Integration',
    description: 'Conecte-se com os principais sistemas ERP do mercado.',
    icon: <SettingsIcon sx={{ fontSize: 40 }} />,
    status: 'coming-soon' as const,
  },
];

const Integrations: React.FC = () => {
  return (
    <>
      <Header />
      <Box sx={{ minHeight: '100vh', backgroundColor: '#fafafa' }}>
        <Container maxWidth="lg" sx={{ py: 4 }}>
          <Typography variant="h4" component="h1" gutterBottom sx={{ mb: 4 }}>
            Integrações
          </Typography>
          <Typography variant="body1" color="text.secondary" sx={{ mb: 6 }}>
            Conecte sua aplicação com nossa plataforma através de diversas integrações disponíveis.
          </Typography>

          <Box sx={{ display: 'grid', gridTemplateColumns: { xs: '1fr', sm: 'repeat(2, 1fr)', md: 'repeat(3, 1fr)' }, gap: 4 }}>
            {integrations.map((integration, index) => (
              <Box key={index}>
                <IntegrationCard {...integration} />
              </Box>
            ))}
          </Box>

          <Box sx={{ mt: 8 }}>
            <Typography variant="h5" gutterBottom>
              Documentação da API
            </Typography>
            <Paper sx={{ p: 4, mt: 2 }}>
              <Typography variant="body1" paragraph>
                Nossa API é totalmente documentada e permite que você integre facilmente sua aplicação com nossa plataforma.
              </Typography>
              <Button variant="contained" color="primary" startIcon={<CodeIcon />}>
                Ver Documentação
              </Button>
            </Paper>
          </Box>
        </Container>
      </Box>
    </>
  );
};

export default Integrations; 