import React from 'react';
import { ThemeProvider } from '@mui/material';
import { BrowserRouter as Router, Routes, Route } from 'react-router-dom';
import { theme } from './theme/theme';
import Dashboard from './pages/Dashboard/Dashboard';
import Marketplace from './pages/Marketplace/Marketplace';
import Integrations from './pages/Integrations/Integrations';
import Finances from './pages/Finances/Finances';
import Support from './pages/Support/Support';

const App = () => {
  return (
    <ThemeProvider theme={theme}>
      <Router>
        <Routes>
          <Route path="/" element={<Dashboard />} />
          <Route path="/dashboard" element={<Dashboard />} />
          <Route path="/marketplace" element={<Marketplace />} />
          <Route path="/integracoes" element={<Integrations />} />
          <Route path="/financas" element={<Finances />} />
          <Route path="/suporte" element={<Support />} />
        </Routes>
      </Router>
    </ThemeProvider>
  );
};

export default App;
