import React from 'react';
import {
  AppBar,
  Toolbar,
  IconButton,
  Badge,
  Avatar,
  styled,
  Box,
  Typography,
} from '@mui/material';
import {
  NotificationsOutlined as NotificationsIcon,
  SearchOutlined as SearchIcon,
} from '@mui/icons-material';

const StyledAppBar = styled(AppBar)(({ theme }) => ({
  backgroundColor: theme.palette.background.paper,
  color: theme.palette.text.primary,
  boxShadow: 'none',
  borderBottom: `1px solid ${theme.palette.divider}`,
  left: 280,
  width: 'calc(100% - 280px)',
}));

const SearchBox = styled(Box)(({ theme }) => ({
  backgroundColor: theme.palette.background.default,
  borderRadius: theme.shape.borderRadius,
  padding: '8px 16px',
  display: 'flex',
  alignItems: 'center',
  width: '300px',
  gap: '8px',
}));

const Header = () => {
  return (
    <StyledAppBar position="fixed">
      <Toolbar>
        <SearchBox>
          <SearchIcon color="action" />
          <input
            style={{
              border: 'none',
              background: 'none',
              outline: 'none',
              width: '100%',
              fontSize: '14px',
            }}
            placeholder="Pesquisar..."
          />
        </SearchBox>
        <Box sx={{ flexGrow: 1 }} />
        <IconButton size="large">
          <Badge badgeContent={4} color="error">
            <NotificationsIcon />
          </Badge>
        </IconButton>
        <Box
          sx={{
            display: 'flex',
            alignItems: 'center',
            gap: 2,
            ml: 2,
            cursor: 'pointer',
          }}
        >
          <Avatar
            sx={{ width: 40, height: 40 }}
            src="https://i.pravatar.cc/300"
          />
          <Box>
            <Typography variant="subtitle1" fontWeight="medium">
              João Silva
            </Typography>
            <Typography variant="body2" color="text.secondary">
              Administrador
            </Typography>
          </Box>
        </Box>
      </Toolbar>
    </StyledAppBar>
  );
};

export default Header; 