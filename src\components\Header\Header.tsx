import React from 'react';
import {
  AppBar,
  Box,
  Toolbar,
  IconButton,
  Typography,
  InputBase,
  Avatar,
  Container,
} from '@mui/material';
import { Search as SearchIcon } from '@mui/icons-material';
import { Link } from 'react-router-dom';

const Header = () => {
  return (
    <AppBar position="sticky" color="default" elevation={1} sx={{ backgroundColor: 'white' }}>
      <Container maxWidth={false} sx={{ maxWidth: '2000px' }}>
        <Toolbar sx={{ display: 'flex', gap: 4, justifyContent: 'space-between' }}>
          {/* Logo */}
          <Link to="/" style={{ textDecoration: 'none', display: 'flex', alignItems: 'center' }}>
          <Box
  component="img"
  src="/logotipo.png"
  alt="ZucroPay"
  sx={{
    height: 65,
    width: 'auto',
    objectFit: 'contain',
    display: 'block',
    marginLeft: 2,
  }}
/>
          </Link>

          {/* Search and Navigation Container */}
          <Box
            sx={{
              display: 'flex',
              alignItems: 'center',
              gap: 4,
              flex: 1,
              justifyContent: 'center',
            }}
          >
            {/* Search Bar */}
            <Box
              sx={{
                position: 'relative',
                backgroundColor: '#f8f9fa',
                borderRadius: 2,
                width: '300px',
                display: 'flex',
                alignItems: 'center',
              }}
            >
              <IconButton sx={{ p: 1 }}>
                <SearchIcon />
              </IconButton>
              <InputBase
                placeholder="Pesquisar..."
                sx={{
                  flex: 1,
                  '& .MuiInputBase-input': {
                    p: 1,
                  },
                }}
              />
            </Box>

            {/* Navigation Links */}
            <Box sx={{ display: 'flex', gap: 4 }}>
              <Link to="/dashboard" style={{ textDecoration: 'none', color: '#5818C8', display: 'flex', alignItems: 'center', gap: 1 }}>
                Dashboard
              </Link>
              <Link to="/vendas" style={{ textDecoration: 'none', color: 'text.primary', display: 'flex', alignItems: 'center', gap: 1 }}>
                Vendas
              </Link>
              <Link to="/produtos" style={{ textDecoration: 'none', color: 'text.primary', display: 'flex', alignItems: 'center', gap: 1 }}>
                Produtos
              </Link>
              <Link to="/marketplace" style={{ textDecoration: 'none', color: 'text.primary', display: 'flex', alignItems: 'center', gap: 1 }}>
                Marketplace
              </Link>
              <Link to="/integracoes" style={{ textDecoration: 'none', color: 'text.primary', display: 'flex', alignItems: 'center', gap: 1 }}>
                Integrações
              </Link>
              <Link to="/financas" style={{ textDecoration: 'none', color: 'text.primary', display: 'flex', alignItems: 'center', gap: 1 }}>
                Finanças
              </Link>
              <Link to="/indique" style={{ textDecoration: 'none', color: 'text.primary', display: 'flex', alignItems: 'center', gap: 1 }}>
                Indique e Ganhe
              </Link>
              <Link to="/suporte" style={{ textDecoration: 'none', color: 'text.primary', display: 'flex', alignItems: 'center', gap: 1 }}>
                Suporte
              </Link>
            </Box>
          </Box>

          {/* User Profile */}
          <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
            <Avatar sx={{ width: 32, height: 32 }}>AM</Avatar>
            <Box>
              <Typography variant="body2" sx={{ fontWeight: 500 }}>
                Anderson Moura
              </Typography>
              <Typography variant="caption" color="text.secondary">
                Dev
              </Typography>
            </Box>
          </Box>
        </Toolbar>
      </Container>
    </AppBar>
  );
};

export default Header;
