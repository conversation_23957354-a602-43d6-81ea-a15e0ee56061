import React from 'react';
import {
  Drawer,
  List,
  ListItem,
  ListItemIcon,
  ListItemText,
  styled,
  Box,
  Typography,
} from '@mui/material';
import {
  Dashboard as DashboardIcon,
  Payment as PaymentIcon,
  People as PeopleIcon,
  Settings as SettingsIcon,
  Assessment as AssessmentIcon,
} from '@mui/icons-material';
import { useNavigate, useLocation } from 'react-router-dom';

const DRAWER_WIDTH = 280;

const StyledDrawer = styled(Drawer)({
  width: DRAWER_WIDTH,
  flexShrink: 0,
  '& .MuiDrawer-paper': {
    width: DRAWER_WIDTH,
    boxSizing: 'border-box',
    backgroundColor: '#1e293b',
    color: 'white',
  },
});

const Logo = styled(Box)({
  padding: '24px',
  display: 'flex',
  alignItems: 'center',
  gap: '12px',
});

const StyledListItem = styled(ListItem)<{ active?: boolean }>(({ active }) => ({
  margin: '8px 16px',
  borderRadius: '8px',
  backgroundColor: active ? 'rgba(255, 255, 255, 0.08)' : 'transparent',
  '&:hover': {
    backgroundColor: 'rgba(255, 255, 255, 0.12)',
  },
}));

const menuItems = [
  { text: 'Dashboard', icon: <DashboardIcon />, path: '/' },
  { text: 'Transações', icon: <PaymentIcon />, path: '/transactions' },
  { text: 'Clientes', icon: <PeopleIcon />, path: '/customers' },
  { text: 'Relatórios', icon: <AssessmentIcon />, path: '/reports' },
  { text: 'Configurações', icon: <SettingsIcon />, path: '/settings' },
];

const Sidebar = () => {
  const navigate = useNavigate();
  const location = useLocation();

  return (
    <StyledDrawer variant="permanent">
      <Logo>
        <img src="/logo.svg" alt="ZucroPay" height="32" />
        <Typography variant="h5" fontWeight="bold">
          ZucroPay
        </Typography>
      </Logo>
      <List>
        {menuItems.map((item) => (
          <StyledListItem
            button
            key={item.text}
            onClick={() => navigate(item.path)}
            active={location.pathname === item.path}
          >
            <ListItemIcon sx={{ color: 'white', minWidth: 40 }}>
              {item.icon}
            </ListItemIcon>
            <ListItemText primary={item.text} />
          </StyledListItem>
        ))}
      </List>
    </StyledDrawer>
  );
};

export default Sidebar; 