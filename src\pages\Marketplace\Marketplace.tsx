import React from 'react';
import { Box, Typography, Container, Paper } from '@mui/material';
import StorefrontIcon from '@mui/icons-material/Storefront';
import ShoppingCartIcon from '@mui/icons-material/ShoppingCart';
import LocalOfferIcon from '@mui/icons-material/LocalOffer';
import InventoryIcon from '@mui/icons-material/Inventory';
import Header from '../../components/Header/Header';

interface MarketplaceCardProps {
  title: string;
  description: string;
  icon: React.ReactNode;
  color: string;
}

const MarketplaceCard: React.FC<MarketplaceCardProps> = ({ title, description, icon, color }) => (
  <Paper
    sx={{
      p: 3,
      height: '100%',
      transition: 'transform 0.2s, box-shadow 0.2s',
      '&:hover': {
        transform: 'translateY(-4px)',
        boxShadow: 3,
      },
      borderTop: `4px solid ${color}`,
    }}
  >
    <Box sx={{ color: color, mb: 2 }}>
      {icon}
    </Box>
    <Typography variant="h6" gutterBottom>
      {title}
    </Typography>
    <Typography variant="body2" color="text.secondary">
      {description}
    </Typography>
  </Paper>
);

const marketplaceFeatures = [
  {
    title: 'Produtos em Destaque',
    description: 'Explore nossa seleção de produtos em destaque com as melhores ofertas do mercado.',
    icon: <StorefrontIcon sx={{ fontSize: 40 }} />,
    color: '#4CAF50',
  },
  {
    title: 'Carrinho Inteligente',
    description: 'Gerencie suas compras com nosso carrinho inteligente e checkout simplificado.',
    icon: <ShoppingCartIcon sx={{ fontSize: 40 }} />,
    color: '#2196F3',
  },
  {
    title: 'Ofertas Especiais',
    description: 'Aproveite descontos exclusivos e promoções especiais para nossos clientes.',
    icon: <LocalOfferIcon sx={{ fontSize: 40 }} />,
    color: '#FF9800',
  },
  {
    title: 'Gestão de Estoque',
    description: 'Controle seu inventário de forma eficiente com nossas ferramentas de gestão.',
    icon: <InventoryIcon sx={{ fontSize: 40 }} />,
    color: '#9C27B0',
  },
];

const Marketplace: React.FC = () => {
  return (
    <>
      <Header />
      <Box sx={{ minHeight: '100vh', backgroundColor: '#fafafa' }}>
        <Container maxWidth="lg" sx={{ py: 4 }}>
          <Typography variant="h4" component="h1" gutterBottom sx={{ mb: 4 }}>
            Marketplace
          </Typography>
          <Typography variant="body1" color="text.secondary" sx={{ mb: 6 }}>
            Explore nosso marketplace e descubra uma variedade de produtos e serviços para impulsionar seu negócio.
          </Typography>
          
          <Box sx={{ display: 'grid', gridTemplateColumns: 'repeat(auto-fit, minmax(250px, 1fr))', gap: 4 }}>
            {marketplaceFeatures.map((feature, index) => (
              <Box key={index}>
                <MarketplaceCard {...feature} />
              </Box>
            ))}
          </Box>

          <Box sx={{ mt: 8 }}>
            <Typography variant="h5" gutterBottom>
              Produtos em Destaque
            </Typography>
            <Box sx={{ display: 'grid', gridTemplateColumns: { xs: '1fr', sm: 'repeat(2, 1fr)', md: 'repeat(3, 1fr)' }, gap: 4 }}>
              {[1, 2, 3].map((product) => (
                <Paper
                  key={product}
                  sx={{
                    p: 3,
                    height: '100%',
                    display: 'flex',
                    flexDirection: 'column',
                    gap: 2,
                  }}
                >
                  <Box
                    sx={{
                      height: 200,
                      backgroundColor: '#f5f5f5',
                      display: 'flex',
                      alignItems: 'center',
                      justifyContent: 'center',
                    }}
                  >
                    <StorefrontIcon sx={{ fontSize: 60, color: '#bdbdbd' }} />
                  </Box>
                  <Typography variant="h6">Produto {product}</Typography>
                  <Typography variant="body2" color="text.secondary">
                    Descrição do produto {product} com detalhes e especificações.
                  </Typography>
                  <Typography variant="h6" color="primary" sx={{ mt: 'auto' }}>
                    R$ {(Math.random() * 1000).toFixed(2)}
                  </Typography>
                </Paper>
              ))}
            </Box>
          </Box>
        </Container>
      </Box>
    </>
  );
};

export default Marketplace; 